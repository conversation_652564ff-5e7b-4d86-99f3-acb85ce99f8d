<?php
// +----------------------------------------------------------------------
// | 青羊区基层治理平台
// +----------------------------------------------------------------------
// | Author: 盘古之星
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace App\Extends\Helpers;


use App\Extends\Helpers\Interface\YZTApi;
use App\Extends\Services\System\DictDataService;
use App\Extends\Services\System\StructService;
use App\Models\Grid\AppearPoints;
use App\Models\Street\House;
use App\Models\Street\Quarters;
use App\Models\Street\ResidentTag;
use App\Models\Street\Shop;
use App\Models\System\Admin;
use App\Models\System\AdminManageStruct;
use App\Models\System\Industry;
use App\Models\System\GridGrid;
use App\Models\System\GridGridUnit;
use App\Models\System\GridMicrogridUnit;
use App\Models\System\Label;
use App\Models\System\LabelGroup;
use App\Models\System\Struct;
use App\Models\System\Users;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class Functions
{

    /**
     * stdClass转数组
     * @param $obj
     * @return array
     */
    public static function stdToArray($obj): array
    {
        if ($obj) {
            $obj = json_decode(json_encode($obj), true);
        } else {
            $obj = [];
        }
        return $obj;
    }

    /**
     * 文件添加域名
     * @param string|null $file
     * @return string
     */
    public static function getFileUrl(string $file = null): string
    {
        if (!$file) return '';
        if (strpos($file, 'data:image') === 0) {

        } elseif (strpos($file, 'http') !== 0) {
            $domain = config('ffsm.fileDomain');
            if (!$domain) {
                $domain = url('/');
            }
            $file = str_replace('//', '/', '/' . $file);
            $file = $domain . $file;
        }
        return $file;
    }

    /**
     * 获取客户端ip
     * @return string
     */
    public static function getClientIp(): string
    {
        if (getenv('HTTP_CLIENT_IP')) {
            $ip = getenv('HTTP_CLIENT_IP');
        } else if (getenv('HTTP_X_FORWARDED_FOR')) {
            $ip = getenv('HTTP_X_FORWARDED_FOR');
        } else if (getenv('REMOTE_ADDR')) {
            $ip = getenv('REMOTE_ADDR');
        } else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        return $ip;
    }


    /**
     * 将字符串根据换行、分号、逗号转为数组，并可以再次根据设置分数组
     * @param $value
     * @param string $sediff
     * @return array|false|string[]
     */
    public static function strLineToArray($value, $sediff = '')
    {
        if ($value) {
            $array = preg_split('/[,;\r\n]+/', trim($value, ",;\r\n"));
            if ($sediff && strpos($value, $sediff)) {
                $value = [];
                foreach ($array as $val) {
                    list($k, $v) = explode($sediff, $val);
                    $value[$k] = $v;
                }
            } else {
                $value = $array;
            }
        } else {
            $value = [];
        }
        return $value;
    }

    /**
     * curl的POST请求
     * @param $url
     * @param $array
     * @return bool|string
     */
    public static function b5curl_post($url, $array,$header = [],$method = ''): bool|string
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        if (!empty($header)){
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        }
        if ($method != ''){
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method); //设置请求方式
        }
        $proxy = env('PROXY_URL', '');
        if ($proxy) {
            curl_setopt($curl, CURLOPT_PROXY, $proxy);
        }
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        $post_data = $array;
        curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);
        $data = curl_exec($curl);
        curl_close($curl);
        return $data;
    }

    /**
     * curl的GET请求
     * @param $url
     * @return bool|string
     */
    public static function b5curl_get($url,$header = []): bool|string
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        if (!empty($header)){
            curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        }
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_HEADER, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        $proxy = env('PROXY_URL', '');
        if ($proxy) {
            curl_setopt($ch, CURLOPT_PROXY, $proxy);
        }
        $response = curl_exec($ch);
        if ($response === false) {
            $error = curl_error($ch);
            curl_close($ch); // 关闭cURL会话
            Log::info("cURL Error: " . $error);
        }
        curl_close($ch);
        return $response;
    }

    /**
     * url拼接
     * @param string $url
     * @param string $param
     * @return string
     */
    public static function urlContact(string $url, string $param = ''): string
    {
        if (!$param) return $url;
        if (str_contains($url, '?')) {
            $url = $url . '&' . $param;
        } else {
            $url = $url . '?' . $param;
        }
        return $url;
    }

    /**
     * 字符串替换
     * @param $url
     * @param $text
     * @param $oldtext
     * @return array|string|string[]
     */
    public static function textReplaceStr($url, $text, $oldtext)
    {
        return str_replace($oldtext, $text, $url);
    }

    /**
     * 获取房屋TREE
     * @param $res_id
     * @param $is_house
     * @return array
     */
    public static function getHouseTree($res_id = 0, $is_house = 0)
    {
        if (!$res_id)
            return [];
        //获取楼栋
        $house = [];
        if ($is_house == 1) {
            $build = House::query()
                ->select("id", "building", "unit", "floor", "sn")
                ->where("res_id", $res_id)
                ->orderBy("building", "asc")
                ->get()->toArray();
        } else {
            $build = House::query()
                ->select("building", "unit", "floor")
                ->where("res_id", $res_id)
                ->groupBy("building", "unit", "floor")
                ->orderBy("building", "asc")
                ->orderBy("unit", "asc")
                ->orderBy("floor", "asc")
                ->get()->toArray();
        }
        if (!empty($build)) {
            foreach ($build as $b) {
                if ($is_house == 2) {
                    $house[$b['building']]['id'] = "b_" . $b['building'];
                    $house[$b['building']]['title'] = $b['building'] . "栋";
                    $house[$b['building']]['parent_id'] = 0;
                    $house[$b['building']]['child'][$b['unit']]['id'] = "u_" . $b['unit'];
                    $house[$b['building']]['child'][$b['unit']]['title'] = $b['unit'] . "单元";
                    $house[$b['building']]['child'][$b['unit']]['parent_id'] = "b_" . $b['building'];
                    $house[$b['building']]['child'][$b['unit']]['titles'] = $b['building'] . '栋' . $b['unit'] . '单元';
                    $house[$b['building']]['child'][$b['unit']]['ids'] = $b['building'] . ',' . $b['unit'];
                } else {
                    $house[$b['building']]['id'] = "b_" . $b['building'];
                    $house[$b['building']]['title'] = $b['building'] . "栋";
                    $house[$b['building']]['parent_id'] = 0;
                    $house[$b['building']]['child'][$b['unit']]['id'] = "u_" . $b['unit'];
                    $house[$b['building']]['child'][$b['unit']]['title'] = $b['unit'] . "单元";
                    $house[$b['building']]['child'][$b['unit']]['parent_id'] = "b_" . $b['building'];
                    $house[$b['building']]['child'][$b['unit']]['child'][$b['floor']]['id'] = "f_" . $b['floor'];
                    $house[$b['building']]['child'][$b['unit']]['child'][$b['floor']]['title'] = $b['floor'] . "楼";
                    $house[$b['building']]['child'][$b['unit']]['child'][$b['floor']]['parent_id'] = "u_" . $b['unit'];
                    $house[$b['building']]['child'][$b['unit']]['child'][$b['floor']]['titles'] = $b['building'] . '栋' . $b['unit'] . '单元' . $b['floor'] . "楼";
                    $house[$b['building']]['child'][$b['unit']]['child'][$b['floor']]['ids'] = $b['building'] . ',' . $b['unit'] . ',' . $b['floor'];
                    if ($is_house == 1) {
                        $house[$b['building']]['child'][$b['unit']]['child'][$b['floor']]['child'][] = [
                            'id' => $b['id'],
                            'title' => $b['sn'] . "号",
                            'parent_id' => "f_" . $b['floor'],
                            'titles' => $b['building'] . '栋' . $b['unit'] . '单元' . $b['floor'] . "楼" . $b['sn'] . "号"
                        ];
                    }
                }
            }
        }
        if ($is_house == 2) {
            return array_map(function ($value) {
                $value['child'] = array_values($value['child']);
                return $value;
            }, array_values($house));
        } else {
            return array_map(function ($value) {
                $value['child'] = array_map(function ($value) {
                    $value['child'] = array_values($value['child']);
                    return $value;
                }, $value['child']);
                $value['child'] = array_values($value['child']);
                return $value;
            }, array_values($house));
        }
    }

    /**
     * 获取小区基本信息
     * @param $res_id
     * @return array
     */
    public static function getQuartersInfo($res_id = 0)
    {
        if (!$res_id) {
            return [];
        }
        return Quarters::bFind($res_id);
    }

    /**
     * 转化房号
     * @param $id
     * @param $is_house 是否带房号
     * @param $is_res 是否带小区
     * @return string
     */
    public static function getHouseName($id = 0, $is_house = 0, $is_res = 0)
    {
        if (!$id) {
            return '';
        }
        $info = House::bFind($id);
        if (empty($info)) {
            return '';
        }
        $houseStr = "{$info['building']}栋{$info['unit']}单元{$info['floor']}楼";
        if ($is_res) {
            //获取小区
            $res_name = Quarters::query()->where("id", $info['res_id'])->value("name");
            $houseStr = $res_name . "-" . $houseStr;
        }
        if ($is_house) {
            $houseStr = $houseStr . $info['sn'] . "号";
        }
        return $houseStr;
    }

    /**
     * 获取商铺信息
     * @param $shopid
     * @param $is_shopname
     * @return array
     */
    public static function getShopName($shopid = 0, $is_shopname = 0)
    {
        $info = Shop::bFind($shopid);
        if (empty($info) && $is_shopname)
            return '';
        if (empty($info) && !$is_shopname)
            return [];
        if ($is_shopname) {
            return $info['name'];
        } else {
            return $info;
        }

    }

    /**
     * 获取社区
     * @return mixed[]
     */
    public static function getCommunity($cid = 0)
    {
        static $community = [];
        if (!isset($community[$cid])) {
            $community[$cid] = Struct::query()
                ->where("struct_type", StructService::TYPE_COMMUNITY)
                ->when($cid, function ($query) use ($cid) {
                    $query->where("id", $cid);
                })
                ->where("parent_id", config('ffsm.root_struct_id'))
                ->pluck("name", "id")
                ->toArray();
        }
        return $community[$cid];
    }

    /**
     * 获取科室
     * @param int $id
     * @return array
     * @author: duyang
     * @time: 2024/7/18 10:24
     */
    public static function getStruct($id = 0)
    {
        static $struct = [];
        if (!isset($struct[$id])) {
            $struct[$id] = Struct::query()
                ->where("struct_type", StructService::TYPE_DEPARTMENT)
                ->when($id, function ($query) use ($id) {
                    $query->where("id", $id);
                })
                ->where("parent_id", config('ffsm.root_struct_id'))
                ->pluck("name", "id")
                ->toArray();
        }
        return $struct[$id];
    }


    /**
     * 获取社区小区
     * @return array
     */
    public static function getQuarters($cid = 0)
    {
        $quarters = Quarters::query()
            ->when($cid, function ($query) use ($cid) {
                $query->where("cid", $cid);
            })
            ->pluck("name", "id")
            ->toArray();
        return $quarters;
    }

    /**
     * 获取商户行业分类，只有两级
     * @return array
     */
    public static function getIndustrys($pid = 0)
    {
        $industrys = Industry::query()
            ->when($pid >= 0, function ($query) use ($pid) {
                $query->where("parent_id", $pid);
            })
            ->pluck("name", "id")
            ->toArray();
        return $industrys;
    }


    /**
     * 根据用户信息获取网格ID
     * @param $user_id
     * @return int|mixed
     */
    public static function userToGrid($user_id = 0, $is_userinfo = 0)
    {
        if (!$user_id) {
            return 0;
        }
        if ($is_userinfo) {
            $info = Users::query()->where("user_type", 1)->where("id", $user_id)->first();
            return $info ? $info->toArray() : [];
        }
        return Users::query()->where("user_type", 1)->where("id", $user_id)->value("group_id") ?? 0;
    }

    /**
     * 获取字典数据
     * @param $key
     * @return array
     */
    public static function getDictDate($key = "")
    {
        if ($key == '')
            return [];
        return DictDataService::getDataList($key, true);
    }

    public static function getPerpoleTag()
    {
        //获取分组
        $group = LabelGroup::query()->select("id", "name")->get()->toArray();
        if (!empty($group)) {
            foreach ($group as &$item) {
                $item['lable'] = Label::query()->select("id", "name")->where("group_id", $item['id'])->get()->toArray();
            }
        }
        return $group;
    }

    public static function getResTagGroup($res_id = 0)
    {
        if (!$res_id) {
            return [];
        }
        $groupTag = [];
        //获取人员标签
        $tags = ResidentTag::query()->where("resident_id", $res_id)->pluck("tag_id")->toArray();
        if (!empty($tags)) {
            $label = Label::query()->whereIn("id", $tags)->get()->toArray();
            $group = array_unique(array_column($label, "group_id"));
            $group = LabelGroup::query()->whereIn("id", $group)->pluck("name", "id")->toArray();
            foreach ($label as $item) {
                if (isset($group[$item['group_id']])) {
                    $groupTag[$item['group_id']]['g_name'] = $group[$item['group_id']];
                    $groupTag[$item['group_id']]['g_id'] = $item['group_id'];
                    $groupTag[$item['group_id']]['tag'][] = [
                        'tag_name' => $item['name'],
                        'tag_id' => $item['id'],
                    ];
                }
            }
        }
        return $groupTag;
    }

    /**
     * 根据经纬度获取地址
     * @param $address
     * @param $lnglat
     * @return mixed|string
     */
    public static function getAddressByLngLat($address, $lnglat)
    {
        $address = request()->input($address, '');
        if ($address != '') {
            return $address;
        }
        if (!$lnglat) {
            return '';
        }
        return YZTApi::getAddressByLngLat($lnglat);
//        $lnglat = explode(',', $lnglat);
//        if (count($lnglat) != 2) {
//            return '';
//        }
//        $lnglat[0] = round(floatval($lnglat[0]), 6);
//        $lnglat[1] = round(floatval($lnglat[1]), 6);
//        $lnglat = implode(',', $lnglat);
//        $url = 'https://restapi.amap.com/v3/geocode/regeo?key=' . env('GAODE_SERVICE_KEY') . '&location=' . $lnglat;
////        Log::info("地图转化：".$url);
//        $response = self::b5curl_get($url);
////        Log::info("地图转化：".$response);
//        if (!$response){
//            return '';
//        }
//        $data = json_decode($response, true);
//        if (isset($data['status']) && $data['status'] == 1) {
//            return $data['regeocode']['formatted_address'];
//        }
//        return '';
    }

    /**
     * 根据时间范围获取经纬度组
     * @param $start_time
     * @param $end_time
     * @return array
     */
    public static function datescopeGetlnglat($user_id, $start_time, $end_time)
    {
        $start_time = strtotime($start_time);
        $end_time = strtotime($end_time);
        $position = AppearPoints::query()->where("user_id", $user_id)
            ->whereBetween("send_time", [$start_time, $end_time])
            ->pluck("position")->toArray();
        if (env('IS_TEST_GJ')) {
            return [[116.478935, 39.997761], [116.478939, 39.997825], [116.478912, 39.998549], [116.478912, 39.998549],
                [116.478998, 39.998555], [116.478998, 39.998555], [116.479282, 39.99856], [116.479658, 39.998528],
                [116.480151, 39.998453], [116.480784, 39.998302], [116.480784, 39.998302], [116.481149, 39.998184],
                [116.481573, 39.997997], [116.481863, 39.997846], [116.482072, 39.997718], [116.482362, 39.997718],
                [116.483633, 39.998935], [116.48367, 39.998968], [116.484648, 39.999861]];
        }
        $result = [];
        if (!empty($position)) {
            foreach ($position as $item) {
                $result[] = explode(",", $item);
            }
        }
        return $result;
    }

    /**
     * 数字转中文数字
     * @param mixed $number
     * @author: duyang
     * @time: 2024/6/27 18:11
     */
    public static function numToChinese(mixed $number)
    {
        $chiNum = array('零', '一', '二', '三', '四', '五', '六', '七', '八', '九');
        $chiUni = array('', '十', '百', '千', '万', '亿', '十', '百', '千');

        $chiStr = '';

        $num_str = (string)$number;

        $count = strlen($num_str);
        $last_flag = true; //上一个 是否为0
        $zero_flag = true; //是否第一个
        $temp_num = null; //临时数字

        $chiStr = '';//拼接结果
        if ($count == 2) {//两位数
            $temp_num = $num_str[0];
            $chiStr = $temp_num == 1 ? $chiUni[1] : $chiNum[$temp_num] . $chiUni[1];
            $temp_num = $num_str[1];
            $chiStr .= $temp_num == 0 ? '' : $chiNum[$temp_num];
        } else if ($count > 2) {
            $index = 0;
            for ($i = $count - 1; $i >= 0; $i--) {
                $temp_num = $num_str[$i];
                if ($temp_num == 0) {
                    if (!$zero_flag && !$last_flag) {
                        $chiStr = $chiNum[$temp_num] . $chiStr;
                        $last_flag = true;
                    }
                } else {
                    $chiStr = $chiNum[$temp_num] . $chiUni[$index % 9] . $chiStr;
                    $zero_flag = false;
                    $last_flag = false;
                }
                $index++;
            }
        } else {
            $chiStr = $chiNum[$num_str[0]];
        }
        return $chiStr;
    }

    public static function chineseToNum($str)
    {
        $map = array(
            '一' => '1', '二' => '2', '三' => '3', '四' => '4', '五' => '5', '六' => '6', '七' => '7', '八' => '8', '九' => '9',
            '壹' => '1', '贰' => '2', '叁' => '3', '肆' => '4', '伍' => '5', '陆' => '6', '柒' => '7', '捌' => '8', '玖' => '9',
            '零' => '0', '两' => '2',
            '仟' => '千', '佰' => '百', '拾' => '十',
            '万万' => '亿',
        );

        $str = str_replace(array_keys($map), array_values($map), $str);
        $str = self::checkString($str, '/([\d亿万千百十]+)/u');

        $func_c2i = function ($str, $plus = false) use (&$func_c2i) {
            if (false === $plus) {
                $plus = array('亿' => 100000000, '万' => 10000, '千' => 1000, '百' => 100, '十' => 10,);
            }

            $i = 0;
            if ($plus)
                foreach ($plus as $k => $v) {
                    $i++;
                    if (strpos($str, $k) !== false) {
                        $ex = explode($k, $str, 2);
                        $new_plus = array_slice($plus, $i, null, true);
                        $l = $func_c2i($ex[0], $new_plus);
                        $r = $func_c2i($ex[1], $new_plus);
                        if ($l == 0) $l = 1;
                        return $l * $v + $r;
                    }
                }

            return (int)$str;
        };
        return $func_c2i($str);
    }

    //来自uct php微信开发框架，其中的checkString函数如下
    public static function checkString($var, $check = '', $default = '')
    {
        if (!is_string($var)) {
            if (is_numeric($var)) {
                $var = (string)$var;
            } else {
                return $default;
            }
        }
        if ($check) {
            return (preg_match($check, $var, $ret) ? $ret[1] : $default);
        }

        return $var;
    }

    public static function getAllFloorTreeByCid($cid)
    {
        $res = Quarters::query()
            ->where('cid', $cid)
            ->pluck('name', 'id')
            ->toArray();
        $tree = House::query()
            ->select(['res_id', 'building', 'unit'])
            ->whereIn('res_id', array_keys($res))
            ->groupBy('res_id')
            ->groupBy('building')
            ->groupBy('unit')
            ->get()
            ->toArray();
        $list = [];
        foreach ($tree as $item) {
            $list[] = "{$item['res_id']}_{$item['building']}_{$item['unit']}";
        }
        return $list;
    }
    public static function passwordHash($password): string
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    public static function passwordVerify($password, $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     * 密码正则验证
     * @param string $passwrod
     * @return bool
     */
    public static function passwrodReg($passwrod = '')
    {
        $reg = "/((^(?=.*[a-z])(?=.*[A-Z])(?=.*\W)[\da-zA-Z\W]{8,20}$)|(^(?=.*\d)(?=.*[A-Z])(?=.*\W)[\da-zA-Z\W]{8,20}$)|(^(?=.*\d)(?=.*[a-z])(?=.*\W)[\da-zA-Z\W]{8,20}$)|(^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[\da-zA-Z\W]{8,20}$))/";
        if (preg_match($reg, $passwrod)) {
            return true; //验证通过
        } else {
            return false; //验证失败
        }
    }

    /**
     * 格式化开始时间
     * @param $startTime
     * @return false|string
     * @author: duyang
     * @time: 2024/7/8 20:01
     */
    public static function formatStartDateTime($startTime)
    {
        $startTimestamp = strtotime($startTime);
        if (date('Y-m-d', $startTimestamp) === $startTime) {
            //如果是年月日，则取当天的23:59:59
            return $startTime . ' 00:00:00';
        } elseif (date('Y-m-d H', $startTimestamp) === $startTime) {
            return $startTime . ':00:00';
        } elseif (date('Y-m-d H:i', $startTimestamp) === $startTime) {
            return $startTime . ':00';
        } elseif (date('Y', $startTimestamp) === $startTime) {
            return $startTime . '-01-01 00:00:00';
        } elseif (date('Y-m', $startTimestamp) === $startTime) {
            //根据时间戳获取这个月的第一天
            return date('Y-m-d H:i:s', strtotime("last day of -1 month", $startTimestamp) + 86400);
        }
        return $startTime;
    }

    /**
     * 格式化结束时间
     * @param $endTime
     * @return false|string
     * @author: duyang
     * @time: 2024/7/8 20:01
     */
    public static function formatEndDateTime($endTime)
    {
        $endTimestamp = strtotime($endTime);
        if (date('Y-m-d', $endTimestamp) === $endTime) {
            //如果是年月日，则取当天的23:59:59
            return $endTime . ' 23:59:59';
        } elseif (date('Y-m-d H', $endTimestamp) === $endTime) {
            return $endTime . ':59:59';
        } elseif (date('Y-m-d H:i', $endTimestamp) === $endTime) {
            return $endTime . ':59';
        } elseif (date('Y', $endTimestamp) === $endTime) {
            return $endTime . '-12-31 23:59:59';
        } elseif (date('Y-m', $endTimestamp) === $endTime) {
            //根据时间戳获取这个月的最后一天
            return date('Y-m-d H:i:s', strtotime("first day of +1 month", $endTimestamp) - 1);
        }
        return $endTime;
    }

    /**
     * 获取停留次数
     * @param array $points
     * @param int $stayTime
     * @param int $stayRadius
     * @return array
     * @author: duyang
     * @time: 2023/6/29 14:43
     */
    public static function getStayPoint(array $points, int $stayTime = 1800, int $stayRadius = 100)
    {
        $point = [];
        if (!$points) {
            return [];
        }
        $count = count($points);
        for ($i = 0; $i < $count;) {
            $item = $points[$i];
            $maxTime = $item['send_time'];
            for ($j = $i + 1; $j < $count; $j++) {
                $lng = $points[$j]['lng'];
                $lat = $points[$j]['lat'];
                if (self::getDistance($item['lat'], $item['lng'], $lat, $lng) <= $stayRadius) {
                    $maxTime = $points[$j]['send_time'];
                } else {
                    break;
                }
            }

            if ($maxTime - $item['send_time'] >= $stayTime) {
                $point[] = [
                    'location' => [
                        $item['lng'],
                        $item['lat'],
                    ],
                    'address' => $item['address'],
                    'start_time' => date('Y-m-d H:i:s', $item['send_time']),
                    'end_time' => date('Y-m-d H:i:s', $maxTime),
                    'seconds' => $maxTime - $item['send_time'],
                    'duration' => self::formatTimeBySeconds($maxTime - $item['send_time']),
                    'point_num' => $j - $i + 1,
                ];
                $i = $j;
            } else {
                $i++;
            }
        }
        return $point;
    }

    public static function formatTimeBySeconds($seconds, $is_show_miao = 1)
    {
        $time = '';
        if ($seconds >= 3600) {
            $time .= floor($seconds / 3600) . '小时';
            $seconds = $seconds % 3600;
        }
        if ($seconds >= 60) {
            $time .= floor($seconds / 60) . '分钟';
            $seconds = $seconds % 60;
        }
        if ($is_show_miao && $seconds > 0) {
            $time .= $seconds . '秒';
        }
        return $time;
    }

    public static function formatTime($seconds)
    {
        if ($seconds <= 0) {
            return '已超期';
        }
        $time = [];
        if ($seconds >= 3600) {
            $time[] = floor($seconds / 3600);
            $seconds = $seconds % 3600;
        }
        if ($seconds >= 60) {
            $time[] = floor($seconds / 60);
            $seconds = $seconds % 60;
        } else {
            $time[] = 0;
        }
        $time[] = $seconds;
        return implode(':', $time);
    }

    /**
     * 获取两点之间的距离
     * @param $lat1
     * @param $lng1
     * @param $lat2
     * @param $lng2
     * @return float
     * @author: duyang
     * @time: 2023/6/29 14:43
     */
    private static function getDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = 6367000; //地球半径m
        $lat1 = ($lat1 * pi()) / 180;
        $lng1 = ($lng1 * pi()) / 180;
        $lat2 = ($lat2 * pi()) / 180;
        $lng2 = ($lng2 * pi()) / 180;
        $calcLongitude = $lng2 - $lng1;
        $calcLatitude = $lat2 - $lat1;
        $stepOne = pow(sin($calcLatitude / 2), 2) + cos($lat1) * cos($lat2) * pow(sin($calcLongitude / 2), 2);
        $stepTwo = 2 * asin(min(1, sqrt($stepOne)));
        $calculatedDistance = $earthRadius * $stepTwo;
        return round($calculatedDistance);
    }

    /*
     * 格式化网格的单元楼栋小区
     * @param $id
     * @return array
     * @author: duyang
     * @time: 2024/7/16 16:23
     */
    public static function formatGridUnit($id)
    {
        //得到小区ID
        $bindList = GridGridUnit::query()
            ->where('grid_id', $id)
            ->orderBy('res_id')
            ->orderBy('building')
            ->orderBy('unit')
            ->get()
            ->toArray();
        $resIds = array_unique(array_column($bindList, 'res_id'));
        $allList = House::query()
            ->select(['res_id', 'building', 'unit'])
            ->whereIn('res_id', $resIds)
            ->groupBy('res_id')
            ->groupBy('building')
            ->groupBy('unit')
            ->get()
            ->toArray();
        $allTree = [];
        foreach ($allList as $item) {
            $allTree[$item['res_id']][$item['building']][$item['unit']] = $item['unit'];
        }
        $bindTree = [];
        foreach ($bindList as $item) {
            if (!isset($allTree[$item['res_id']][$item['building']][$item['unit']])) {
                //排除数据错误的单元
                continue;
            }
            $bindTree[$item['res_id']][$item['building']][$item['unit']] = $item['unit'];
        }
        return self::formatUnit($bindTree, $allTree);
    }

    /**
     * 格式化微网格的单元楼栋小区
     * @param $id
     * @return array
     * @author: duyang
     * @time: 2024/7/16 16:23
     */
    public static function formatMicrogridUnit($id)
    {
        //得到小区ID
        $bindList = GridMicrogridUnit::query()
            ->where('microgrid_id', $id)
            ->orderBy('res_id')
            ->orderBy('building')
            ->orderBy('unit')
            ->get()
            ->toArray();
        $resIds = array_unique(array_column($bindList, 'res_id'));
        $allList = House::query()
            ->select(['res_id', 'building', 'unit'])
            ->whereIn('res_id', $resIds)
            ->groupBy('res_id')
            ->groupBy('building')
            ->groupBy('unit')
            ->get()
            ->toArray();
        $allTree = [];
        foreach ($allList as $item) {
            $allTree[$item['res_id']][$item['building']][$item['unit']] = $item['unit'];
        }
        $bindTree = [];
        foreach ($bindList as $item) {
            if (!isset($allTree[$item['res_id']][$item['building']][$item['unit']])) {
                //排除数据错误的单元
                continue;
            }
            $bindTree[$item['res_id']][$item['building']][$item['unit']] = $item['unit'];
        }
        return self::formatUnit($bindTree, $allTree);
    }

    /**
     * 格式化单元楼栋小区
     * @param $bindTree
     * @param $allTree
     * @return array
     * @author: duyang
     * @time: 2024/7/16 16:22
     */
    public static function formatUnit($bindTree, $allTree)
    {
        $res = Quarters::query()->whereIn('id', array_keys($bindTree))->pluck('name', 'id')->toArray();
        //若网格只包含小区的部分楼栋，则填写小区名称后，需填写楼栋，单个楼栋填写楼栋号，连续楼栋填写起始楼栋号-结束楼栋号。如包含某个小区的1栋、3栋和5栋到10栋，则添加三行数据，楼栋分别填写1、3、5-10
        $data = [];
        foreach ($bindTree as $resId => $buildings) {
            $tmp = [
                'res' => $res[$resId],
                'buildings' => '',
                'units' => '',
            ];
            if ($buildings === $allTree[$resId]) {
                //如果小区全部单元全部包含
                $data[] = $tmp;
                continue;
            }
            foreach ($buildings as $building_id => $units) {
                $tmp['buildings'] = $building_id;
                $tmp['units'] = '';
                if ($units == $allTree[$resId][$building_id]) {
                    //如果小区全部单元全部包含
                    $data[] = $tmp;
                    continue;
                }
                //判断单元号是否连续
                $tmp['units'] = implode(',', self::compressNumbers($units));
                $data[] = $tmp;
            }
        }
        //在把相同小区单元号一样的楼栋合并
        $list = collect($data)
            ->groupBy(['res', function ($item) {
                return $item['units'];
            }])
            ->toArray();
        $data = [];
        foreach ($list as $resName => $resList) {
            foreach ($resList as $unit => $item) {
                $data[] = [
                    'res' => $resName,
                    'buildings' => implode(',', self::compressNumbers(array_column($item, 'buildings'))),
                    'units' => $unit,
                ];
            }
        }
        return $data;
    }

    /**
     * 获取数字列表，连续的数字用-表示
     * @param array $numbers
     * @return array
     * @author: duyang
     * @time: 2024/7/16 16:22
     */
    public static function compressNumbers(array $numbers)
    {
        sort($numbers);
        $compressed = [];
        $start = null;
        $end = null;

        foreach ($numbers as $number) {
            if ($start === null) {
                $start = $number;
                $end = $number;
            } elseif ($number == $end + 1) {
                // 数字连续
                $end = $number;
            } else {
                // 数字不连续，处理前一段
                if ($start == $end) {
                    $compressed[] = (string)$start;
                } else {
                    $compressed[] = $start . '-' . $end;
                }
                // 开始新一段
                $start = $number;
                $end = $number;
            }
        }

        // 处理最后一段
        if ($start == $end) {
            $compressed[] = (string)$start;
        } else {
            $compressed[] = $start . '-' . $end;
        }

        return $compressed;
    }

    public static function decompressNumbers($numbers)
    {
        $numbers = is_array($numbers) ? $numbers : explode(',', $numbers);
        $decompressed = [];
        foreach ($numbers as $number) {
            if (str_contains($number, '-')) {
                // 如果数字包含'-'，说明是连续的数字
                list($start, $end) = explode('-', $number);
                for ($j = $start; $j <= $end; $j++) {
                    $decompressed[] = $j;
                }
            } else {
                $decompressed[] = $number;
            }
        }
        return $decompressed;
    }

    public static function getLngLatByAddress(mixed $address)
    {
//        $url = "https://restapi.amap.com/v3/geocode/geo?address={$address}&city=成都&output=JSON&key=" . env('GAODE_SERVICE_KEY', '56bbd3a4d161e3b67731742af140be7a');
//        $res = json_decode(self::b5curl_get($url), true);
        $data = YZTApi::keywordsSearch($address);
        if (isset($data['result'][0]) && isset($data['status']) && $data['status'] == 0) {
            return [
                'state' => 1,
                'msg' => '经纬度获取成功',
                'lnglat' => [
                    $data['result'][0]['location']['lng'],
                    $data['result'][0]['location']['lat'],
                ]
            ];
        }
        return ['state' => 0, 'msg' => '经纬度获取失败,' . $data['result'] ?? ''];
    }

    /**
     * 获取科室分管领导和主要领导
     * @param mixed $struct_id
     * @return array
     * @author: duyang
     * @time: 2024/8/2 17:35
     */
    public static function getLeaderByStructId(mixed $struct_id)
    {
        //先获取分管领导
        $fgIds = AdminManageStruct::query()
            ->where('struct_id', $struct_id)
            ->pluck('admin_id')
            ->toArray();
        $data['fg'] = [];
        if ($fgIds) {
            $data['fg'] = Admin::query()
                ->select(['id', 'username', 'mobile', 'leader_type'])
                ->where('struct_id', StructService::getLeaderId())
                ->where('leader_type', 2)
                ->whereIn('id', $fgIds)
                ->get()
                ->pluck(null, 'id')
                ->toArray();
        }
        $data['main'] = self::getMainLeader();
        return $data;
    }

    public static function getMainLeader()
    {
        return Admin::query()
            ->select(['id', 'username', 'mobile', 'leader_type'])
            ->where('struct_id', StructService::getLeaderId())
            ->where('leader_type', 1)
            ->get()
            ->pluck(null, 'id')
            ->toArray();
    }

    public static function getFgLeader()
    {
        return Admin::query()
            ->select(['id', 'username', 'mobile', 'leader_type'])
            ->where('struct_id', StructService::getLeaderId())
            ->where('leader_type', 2)
            ->get()
            ->pluck(null, 'id')
            ->toArray();
    }


    /**
     * 身份证脱敏
     * @param $idCard
     * @return array|mixed|string|string[]|null
     */
    public static function desensitizeIdCard($idCard)
    {
        if (env("OPEN_IDCARD_MASK", 0) == 0) {
            return $idCard;
        }
        if (!$idCard) {
            return "--";
        }
        // 获取身份证长度
        $length = strlen($idCard);
        if ($length != 18 && $length != 16) {
            return $idCard;
        }
        // 根据身份证长度确定需要替换的位数
        $replaceLength = $length == 18 ? 8 : 6;

        // 创建一个星号字符串用于替换中间部分
        $replacement = str_repeat('*', $replaceLength);

        // 使用正则表达式替换中间部分
        $desensitizedIdCard = preg_replace("/(?<=\d{6})\d{8}(?=\d{3})/", $replacement, $idCard);

        return $desensitizedIdCard;
    }

    public static function maskPhoneNumber($phoneNumber)
    {
        if (env("OPEN_MOBILE_MASK", 0) == 0) {
            return $phoneNumber;
        }
        if (!$phoneNumber) {
            return "--";
        }
        // 正则表达式匹配中国大陆手机号码格式
        $pattern = "/^1\d{10}$/";
        if (preg_match($pattern, $phoneNumber)) {
            // 取出手机号的前3位和后4位，其余用星号代替
            return preg_replace("/(\d{3})\d{4}(\d{4})/", "$1****$2", $phoneNumber);
        } else {
            // 如果不是有效的手机号，可以返回原始字符串或错误信息
            return $phoneNumber;
        }
    }

    /**
     * 判断坐标点是否在电子围栏内
     * @return Boolean
     */
    public static function isPointInPolygon($point, $polygon): bool
    {
        $x = $point['lng'];
        $y = $point['lat'];
        $inside = false;
        $n = count($polygon);
        for ($i = 0, $j = $n - 1; $i < $n; $j = $i++) {
            $xi = $polygon[$i][0];
            $yi = $polygon[$i][1];
            $xj = $polygon[$j][0];
            $yj = $polygon[$j][1];
            $intersect = (($yi > $y) != ($yj > $y)) &&
                ($x < ($xj - $xi) * ($y - $yi) / ($yj - $yi) + $xi);
            if ($intersect) {
                $inside = !$inside;
            }
        }
        return $inside;
    }

    /**
     * 高德转百度
     * @param $longitude
     * @param $latitude
     * @return array
     * @author: duyang
     * @time: 2023/7/20 11:08
     */
    public static function gdToBaidu($longitude, $latitude)
    {
        $longitude = (float)$longitude;
        $latitude = (float)$latitude;
        $pi = pi() * 3000.0 / 180.0;
        $z = sqrt($longitude * $longitude + $latitude * $latitude) + 0.00002 * sin($latitude * $pi);
        $theta = atan2($latitude, $longitude) + 0.000003 * cos($longitude * $pi);
        $longitude = $z * cos($theta) + 0.0065;
        $latitude = $z * sin($theta) + 0.006;
        $data['lng'] = round($longitude, 6);
        $data['lat'] = round($latitude, 6);
        return $data;
    }

    /**
     * 百度经纬度转高德经纬度
     * @param $longitude
     * @param $latitude
     * @return array
     * @author: duyang
     * @time: 2023/5/9 15:05
     */
    public static function baiduToGd($longitude, $latitude)
    {
        $longitude = (float)$longitude;
        $latitude = (float)$latitude;
        $x_pi = pi() * 3000.0 / 180.0;
        $x = $longitude - 0.0065;
        $y = $latitude - 0.006;
        $z = sqrt($x * $x + $y * $y) - 0.00002 * sin($y * $x_pi);
        $theta = atan2($y, $x) - 0.000003 * cos($x * $x_pi);
        $longitude = $z * cos($theta);
        $latitude = $z * sin($theta);
        $data['lng'] = round($longitude, 6);
        $data['lat'] = round($latitude, 6);
        return $data;
    }

    /**
     * 坐标转化 高德转wgs84
     * @param $lng
     * @param $lat
     * @return array|float[]
     */
    public static function  gcj02towgs84($lng, $lat) {
        $pi = 3.1415926535897932384626;
        $ee = 0.00669342162296594323;
        $a = 6378245.0;
        if (self::out_of_china($lng, $lat)) {
            return [$lng, $lat];
        } else {
            $dlat = self::transformlat($lng - 105.0, $lat - 35.0);
            $dlng = self::transformlng($lng - 105.0, $lat - 35.0);
            $radlat = $lat / 180.0 * $pi;
            $magic = sin($radlat);
            $magic = 1 - $ee * $magic * $magic;
            $sqrtmagic = sqrt($magic);
            $dlat = ($dlat * 180.0) / (($a * (1 - $ee)) / ($magic * $sqrtmagic) * $pi);
            $dlng = ($dlng * 180.0) / ($a / $sqrtmagic * cos($radlat) * $pi);
            $mglat = $lat + $dlat;
            $mglng = $lng + $dlng;
            return [$lng * 2 - $mglng, $lat * 2 - $mglat];
        }
    }
    private static function out_of_china($lng, $lat) {
        return ($lng < 72.004 || $lng > 137.8347) || (($lat < 0.8293 || $lat > 55.8271) || false);
    }
    private static function transformlat($lng, $lat) {
        $pi = 3.1415926535897932384626;
        $ret = -100.0 + 2.0 * $lng + 3.0 * $lat + 0.2 * $lat * $lat + 0.1 * $lng * $lat + 0.2 * sqrt(abs($lng));
        $ret += (20.0 * sin(6.0 * $lng * $pi) + 20.0 * sin(2.0 * $lng * $pi)) * 2.0 / 3.0;
        $ret += (20.0 * sin($lat * $pi) + 40.0 * sin($lat / 3.0 * $pi)) * 2.0 / 3.0;
        $ret += (160.0 * sin($lat / 12.0 * $pi) + 320 * sin($lat * $pi / 30.0)) * 2.0 / 3.0;
        return $ret;
    }
    private static function transformlng($lng, $lat) {
        $pi = 3.1415926535897932384626;
        $ret = 300.0 + $lng + 2.0 * $lat + 0.1 * $lng * $lng + 0.1 * $lng * $lat + 0.1 *sqrt(abs($lng));
        $ret += (20.0 * sin(6.0 * $lng * $pi) + 20.0 * sin(2.0 * $lng * $pi)) * 2.0 / 3.0;
        $ret += (20.0 * sin($lng * $pi) + 40.0 * sin($lng / 3.0 * $pi)) * 2.0 / 3.0;
        $ret += (150.0 * sin($lng / 12.0 * $pi) + 300.0 * sin($lng / 30.0 * $pi)) * 2.0 / 3.0;
        return $ret;
    }

    /**
     * 截取中文字符串
     * @param $text
     * @param $length
     * @return mixed|string
     */
    public static function splicString($text, $length = 30)
    {
        if (mb_strlen($text, "UTF-8") > $length) {
            $text = mb_substr($text, 0, $length, "UTF-8") . '...';
        }
        return $text;
    }

    /**
     * 获取网格和网格员
     * @param $cid
     * @return array
     * @author: duyang
     * @time: 2024/10/21 17:29
     */
    public static function getGridAndUser($cid)
    {
        return GridGrid::query()
            ->select(['id', 'name'])
            ->with([
                'user:id,username,mobile,avatar,group_id'
            ])
            ->where('cid', $cid)
            ->orderBy('grid_num')
            ->get()
            ->toArray();
    }

    public static function getFileExt(string $scene_image)
    {
        $ext = explode('.', $scene_image);
        return end($ext);
    }

    public static function saveImageFromUrl($url, $path)
    {
        $path .= '/'.basename($url);
        $storage = Storage::disk('uploads');
        if (file_exists($storage->path($path))) {
            return asset($storage->url($path));
        }
        $response = Http::get($url);
        if ($response->successful()) {
            $imageData = $response->body();
            $storage->put($path, $imageData);
            return asset($storage->url($path));
        }
        return '';
    }

    /**
     * 是否应该强制修改密码
     * @param $last_edit_pwd
     * @return int
     * @author: duyang
     * @time: 2025/2/14 13:58
     */
    public static function forceEditPwd($last_edit_pwd)
    {
        if (!env('FORCE_EDIT_PWD', true)) {
            return 0;
        }
        if (session()->get('login_source') == 'sso') {
            //单点登录不需要强制修改密码
            return 0;
        }
        $force = env('FORCE_EDIT_PWD_DAY', 90);
        if ($last_edit_pwd <= time() - $force * 86400) {
            return 1;
        }
        return 0;
    }

    /**
     * 判断是否为全勤
     * @param $leavePeriods
     * @param $workStart
     * @param $workEnd
     * @param $lunchBreakStart
     * @param $lunchBreakEnd
     * @return bool
     * @author: duyang
     * @time: 2024/3/27 15:54
     */
    public static function isFullDayOff($leavePeriods, $workStart, $workEnd, $lunchBreakStart = null, $lunchBreakEnd = null) {
        usort($leavePeriods, function($a, $b) {
            if ($a['end'] == $b['end']) {
                return 0;
            }
            return ($a['end'] < $b['end']) ? -1 : 1;
        });
        // 将时间字符串转换为时间戳
        $workStartTimestamp = strtotime($workStart);
        $workEndTimestamp = strtotime($workEnd);
        if( $lunchBreakStart && $lunchBreakEnd) {
            $lunchBreakStartTimestamp = strtotime($lunchBreakStart);
            $lunchBreakEndTimestamp = strtotime($lunchBreakEnd);
        }

//        dd(func_get_args());
        // 检查是否全天请假
        $isFullDayOff = true;
        $prevEnd = $workStartTimestamp; // 上一个请假时间段的结束时间

        foreach ($leavePeriods as $period) {
            $startTimestamp = strtotime($period['start']);
            $endTimestamp = strtotime($period['end']);
            //排除跨天请假
            $startTimestamp = max($startTimestamp, $workStartTimestamp);
            $endTimestamp = min($endTimestamp, $workEndTimestamp);
            if ($lunchBreakStart && $lunchBreakEnd) {
                // 跳过午休时间
                if ($startTimestamp <= $lunchBreakEndTimestamp && $lunchBreakStartTimestamp <= $endTimestamp) {
                    //有交集
                    if ($startTimestamp <= $lunchBreakStartTimestamp && $endTimestamp >= $lunchBreakEndTimestamp) {
                        ;//完全包含12-13 无须处理
                    } elseif ($startTimestamp >= $lunchBreakStartTimestamp && $endTimestamp <= $lunchBreakEndTimestamp) {
                        ;//请假时间在 12-13之间  无须处理
                    } elseif ($startTimestamp < $lunchBreakStartTimestamp) {
                        $endTimestamp = $lunchBreakEndTimestamp;//结束时间在12-13
                    } else {
                        $startTimestamp = $lunchBreakStartTimestamp;//开始时间在12-13
                    }
                }
            }



            // 如果请假时间段没有与上一个时间段的结束时间相连，则不是全天请假
            if ($prevEnd < $startTimestamp) {
                $isFullDayOff = false;
                break;
            }

            // 更新上一个请假时间段的结束时间为当前时间段的结束时间
            $prevEnd = $endTimestamp;
        }

        // 检查最后一个请假时间段的结束时间是否晚于或等于下班时间
        if ($isFullDayOff && $prevEnd < $workEndTimestamp) {
            $isFullDayOff = false;
        }

        return $isFullDayOff;
    }

    /**
     * 根据年龄获取出生日期
     * @param $age
     * @param string $date
     * @return string
     */
    public static function getBirthDateByAge($age, string $date = '')
    {
        $referenceDate = $date ? new DateTime($date) : new DateTime();
        $birthYear = $referenceDate->format('Y') - $age;
        $birthMonth = $referenceDate->format('m');
        $birthDay = $referenceDate->format('d');
        $birthDate = clone $referenceDate;
        $birthDate->setDate((int)$birthYear, (int)$birthMonth, (int)$birthDay);
        // 处理2月29日特殊情况
        if ($referenceDate->format('m') == 2 && $referenceDate->format('d') == 29) {
            if (!self::isLeapYear($birthYear)) {
                $birthDate->setDate((int)$birthYear, 2, 28);
            }
        }
        return $birthDate->format('Y-m-d');
    }
    public static function getAgeByBirthDate($birthDate, $referenceDate = null)
    {
        if (!$birthDate) {
            return '--';
        }

        // 如果是字符串，转换为DateTime对象
        if (is_string($birthDate)) {
            $birthDate = new DateTime($birthDate);
        }

        // 如果没有提供参考日期，则使用当前日期
        if ($referenceDate === null) {
            $referenceDate = new DateTime();
        } elseif (is_string($referenceDate)) {
            $referenceDate = new DateTime($referenceDate);
        }

        // 计算年龄差
        $age = $referenceDate->diff($birthDate)->y;
        return $birthDate > $referenceDate ? '--' : $age;
    }
    /**
     * 判断是否为闰年
     *
     * @param int $year 年份
     * @return bool 是否为闰年
     */
    public static function isLeapYear($year) {
        return ($year % 4 == 0 && $year % 100 != 0) || ($year % 400 == 0);
    }
}

