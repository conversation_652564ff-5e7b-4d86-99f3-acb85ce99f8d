<?php
// +----------------------------------------------------------------------
// | 方法数码【商户行业分类】
// +----------------------------------------------------------------------
// | Author: 小蓝哥 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace App\Http\Controllers\Admin\Street;

use App\Extends\Helpers\Admin\LoginAuth;
use App\Extends\Helpers\Functions;
use App\Extends\Helpers\JudgeIdcardNum;
use App\Extends\Helpers\Result;
use App\Extends\Helpers\Upload;
use App\Extends\Libs\AdminCommonAction;
use App\Extends\Services\System\DictDataService;
use App\Models\Street\House;
use App\Models\Street\Quarters;
use App\Models\Street\Resident;
use App\Models\Street\ResidentLog;
use App\Models\Street\ResidentTag;
use App\Models\System\Label;
use App\Models\System\LabelGroup;
use App\Models\System\SelfField;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\IOFactory;

class ResidentController extends Street
{
    use AdminCommonAction;

    protected string $model = Resident::class;

    public function export(){
        $params = $this->request->input();
        if (method_exists($this, 'indexBefore')) {
            $params = $this->indexBefore($params);
        }
        $query = $this->model::getInstance()->listRange();
        $query = $this->indexWhere($query, $params);
        $queryResult = $this->indexQuery($query);
        if(is_array($queryResult)){
            $query = $queryResult['query'];
        }else{
            $query = $queryResult;
        }
        $query = $this->selfQuery($query);
        set_time_limit(0);
        ini_set('memory_limit', '256M');
        header('Content-Encoding: UTF-8');
        header("Content-type:application/vnd.ms-excel;charset=UTF-8");
        header('Content-Disposition: attachment;filename="居民数据' . date('Ymd') . '.csv"');
        //返回导出的字段及字段名
        $attributes = [
            'cname_text' => '所属社区',
            'res_name' => '所属小区',
            'house_name' => '房屋',
            'user_name' => '姓名',
            'sex_name' => '性别',
            'birthday' => '生日',
            'age' => '年龄',
            'mobile' => '联系电话',
            'mobile2' => '第二联系电话',
            'id_card' => '证件号',
            'relationship_text' => '与户主关系',
            'landscape_text' => '政治面貌',
            'tags_text' => '人员标签',
            'extend_text' => '扩展信息'
        ];
        $keys = array_keys($attributes);
        $header = array_values($attributes);
        $fp = fopen('php://output', 'a');
        //添加BOM头，以UTF8编码导出CSV文件，如果文件头未添加BOM头，打开会出现乱码。
        fwrite($fp, chr(0xEF) . chr(0xBB) . chr(0xBF));
        //添加导出标题
        fputcsv($fp, $header);
        $nums = 400; //每次导出数量
        $count = $query->count();
        $step = ceil($count / $nums);//循环次数
        for ($i = 0; $i < $step; $i++) {
            //获取社区居民数据
            $exportData = $query->limit($nums)->offset($i*$nums)->get()->toArray();
            $exportData = $this->indexAfter($exportData);
            $dataexport = [];
            if(!empty($exportData)){
                foreach ($exportData as $k=>$item){
                    $array = "";
                    $extend = $item['extend'] ? json_decode($item['extend'],true) : [];
                    if (!empty($extend)){
                        foreach ($extend as $tend){
                            if (!in_array($tend['type'], [11,12,13]) && isset($tend['value']) && $tend['value'] != ''){
                                $array .= $tend['title']."：".$tend['value']."\r\n";
                            }
                        }
                    }
                    $item['extend_text'] = $array;
                    foreach ($keys as $key){
                        if ($key == 'id_card' || $key == 'mobile' || $key == 'mobile2' || $key == 'birthday'){
                            $item[$key] = "`".$item[$key];
                        }
                        $dataexport[$k][$key] = $item[$key];
                    }

                }
            }
            for ($j = 0; $j < $nums; $j++) {
                if (empty($dataexport)){
                    break;
                }
                $dataexport && fputcsv($fp, array_shift($dataexport));
            }
            unset($dataexport);
            ob_flush();
            flush();
        }
    }
    /**
     * 首页渲染，方便重写
     * @param
     * @return View
     */
    protected function indexRender(): View
    {
        $quarters = [];
        if (C_ID > 0){
            //获取小区
            $quarters = Quarters::query()->select("id","name")->where("cid",C_ID)->get()->toArray();
        }
        return $this->render('', ['input' => $this->request->all(),'quarters' => $quarters,'house_id' => $this->request->get("house_id",0)]);
    }

    public function ajaxcount(){
        $params = $this->request->input();
        if (method_exists($this, 'indexBefore')) {
            $params = $this->indexBefore($params);
        }
        $query = $this->model::getInstance()->listRange();
        $query = $this->indexWhere($query, $params);
        $queryResult = $this->indexQuery($query);
        if(is_array($queryResult)){
            $query = $queryResult['query'];
        }else{
            $query = $queryResult;
        }
        $query = $this->selfQuery($query);
        $community = array_keys(LoginAuth::admin()->auth_communitys);//社区
        if (empty($community)){
            $community[] = 0;
        }
        //统计数据
        $result['count'] = $query->clone()->when(!empty($community),function ($query) use ($community){
            $query->whereIn("cid",$community);
        })->count();
        $result['boy_count'] = $query->clone()->when(!empty($community),function ($query) use ($community){
            $query->whereIn("cid",$community);
        })->where("sex",1)->count();
        $result['girl_count'] = $query->clone()->when(!empty($community),function ($query) use ($community){
            $query->whereIn("cid",$community);
        })->where("sex",2)->count();
        //性别保密
        $result['bm_count'] = $query->clone()->when(!empty($community),function ($query) use ($community){
            $query->whereIn("cid",$community);
        })->where("sex",0)->count();
        $birmin = Functions::getBirthDateByAge(80);
        $result['80_count'] = $query->clone()->when(!empty($community),function ($query) use ($community){
            $query->whereIn("cid",$community);
        })->where("birthday","<=",$birmin)->where("birthday","!=","")->count();
        $birmin = Functions::getBirthDateByAge(6);
        $result['5_count'] = $query->clone()->when(!empty($community),function ($query) use ($community){
            $query->whereIn("cid",$community);
        })
            ->where("birthday",">",$birmin)
            ->where("birthday","!=","")
            ->count();
        $birmin = Functions::getBirthDateByAge(60);
        $birmax = Functions::getBirthDateByAge(80);
        $result['67_count'] = $query->clone()->when(!empty($community),function ($query) use ($community){
            $query->whereIn("cid",$community);
        })
            ->where("birthday","<=",$birmin)
            ->where("birthday",">",$birmax)
            ->where("birthday","!=","")
            ->count();
        //党员
        $result['landscape_count'] = $query->clone()->when(!empty($community),function ($query) use ($community){
            $query->whereIn("cid",$community);
        })->where("landscape","communist_party")->count();
        //租户
        $result['relationship_count'] = $query->clone()->when(!empty($community),function ($query) use ($community){
            $query->whereIn("cid",$community);
        })->where("relationship","zuhu")->count();
        //获取标签数据
        $tags = ResidentTag::query()->select("tag_id")
            ->when(!empty($community),function ($query) use ($community){
                $query->whereIn("cid",$community);
            })
            ->with("label")->orderBy("tag_id","asc")
            ->groupBy("tag_id")->get()->toArray();
        $group = LabelGroup::query()->pluck("name","id")->toArray();
        $tag = [];
        if (!empty($tags)){
            foreach ($tags as $item){
                if (!empty($item['label'])){
                    $tag[$item['label']['group_id']]['group_name'] = $group[$item['label']['group_id']];
                    $tag[$item['label']['group_id']]['tag'][$item['tag_id']]['id'] = $item['tag_id'];
                    $tag[$item['label']['group_id']]['tag'][$item['tag_id']]['name'] = $item['label']['name'];
                    $tag[$item['label']['group_id']]['tag'][$item['tag_id']]['count'] = $query->clone()
                        ->whereHas("tag",function ($query) use ($item){
                            $query->where("tag_id",$item['tag_id']);
                        })->count();
                }
            }
        }
        if (!empty($tag)){
            foreach ($tag as &$tagItem){
                $count =  $query->clone()
                    ->whereHas("tag",function ($query) use ($tagItem){
                        $query->whereIn("tag_id",array_keys($tagItem['tag']));
                    })
                    ->count();
                $tagItem['count'] = $count;
            }
        }
        $result['tag'] = $tag;
        return $this->render('', ['result' => $result,'input' => $this->request->all()]);
    }

    /**
     * @param array $params
     * @return array
     */
    protected function indexBefore(array $params): array
    {
        $sql = "";
        if ((isset($params['age']['min']) && is_numeric($params['age']['min'])) || ($params['type'] == 1 && in_array($params['index'], [3,5]))){
            $ageMin = [];
            if (isset($params['age']['min']) && is_numeric($params['age']['min'])){
                $ageMin[] = $params['age']['min'];
            }
            if ($params['type'] == 1 && in_array($params['index'], [3,5])){
                if ($params['index'] == 3)
                    $ageMin[] = 80;
                if ($params['index'] == 5)
                    $ageMin[] = 60;
            }
            $birmax = Functions::getBirthDateByAge(min($ageMin));
            $sql = "\"birthday\" <= '{$birmax}' and \"birthday\" <> '' and ";
        }
        if ((isset($params['age']['max']) && is_numeric($params['age']['max'])) || ($params['type'] == 1 && in_array($params['index'], [4,5]))){
            $ageMax = [];
            if (isset($params['age']['max']) && is_numeric($params['age']['max'])){
                $ageMax[] = $params['age']['max'];
            }
            if ($params['type'] == 1 && in_array($params['index'], [4,5])){
                if ($params['index'] == 4)
                    $ageMax[] = 5;
                if ($params['index'] == 5)
                    $ageMax[] = 79;
            }
            $birmax = Functions::getBirthDateByAge(max($ageMax) + 1);
            $sql .= "\"birthday\" > '{$birmax}' and \"birthday\" <> '' and ";
        }
        if ($sql != ''){
            $params['sqlraw'] = trim($sql,'and ');
        }
        if ((isset($params['sex']) && $params['sex'] != '') || ($params['type'] == 1 && in_array($params['index'], [1,2, 0]))){
            $sex = [];
            if (isset($params['sex']) && $params['sex'] != ''){
                $sex = [$params['sex']];
            }
            if ($params['type'] == 1 && in_array($params['index'], [1,2, 0])){
                $sex[] = $params['index'];
            }
            $params['in']['sex'] = array_unique($sex);
        }
        if ((isset($params['tag']) && $params['tag'] != '') || ($params['type'] == 2 && $params['index'] > 0)){
            $tag = [];
            if (isset($params['tag']) && $params['tag'] != ''){
                $tag = explode(",", (string)$params['tag']);
            }
            if ($params['type'] == 2 && $params['index'] > 0){
                $tag[] = $params['index'];
            }
            $tag = array_unique($tag);
            $resident_ids = ResidentTag::query()->whereIn("tag_id",$tag)
                ->when(isset($params['cid']) && $params['cid']> 0,function ($query) use($params){
                    $query->where("cid",$params['cid']);
                })->pluck('resident_id')->toArray();
            if (empty($resident_ids)){
                $resident_ids = [0];
            }
            $params['in']['id'] = $resident_ids;
        }
        if ((isset($params['landscape']) && $params['landscape'] != '') || ($params['type'] == 1 && $params['index'] == 6)){
            $landscape = [];
            if (isset($params['landscape']) && $params['landscape'] != ''){
                $landscape[] = $params['landscape'];
            }
            if ($params['type'] == 1 && $params['index'] == 6){
                $landscape[] = "communist_party";
            }
            $landscape = array_unique($landscape);
            $params['in']['landscape'] = $landscape;
        }
        if ((isset($params['relationship']) && $params['relationship'] != '') || ($params['type'] == 1 && $params['index'] == 7)){
            $relationship = [];
            if (isset($params['relationship']) && $params['relationship'] != ''){
                $relationship[] = $params['relationship'];
            }
            if ($params['type'] == 1 && $params['index'] == 7){
                $relationship[] = "zuhu";
            }
            $relationship = array_unique($relationship);
            $params['in']['relationship'] = $relationship;
        }
        $community = LoginAuth::admin()->auth_communitys;//社区
        if ($community){
            $params['in']['cid'] = array_keys($community);
        }else{
            $params['in']['cid'] = [0];
        }
        //判断房屋
        if (isset($params['house_id']) && $params['house_id'] != ''){
            $but = explode(",", $params['house_id']);
            if (count($but) == 2 && isset($params['where']['res_id']) && $params['where']['res_id']>0){
                //查询房号
                $houseIds = House::query()->where("res_id",$params['where']['res_id'])
                    ->where("building",$but[0])
                    ->where("unit",$but[1])
                    ->pluck("id")->toArray();
                if (!empty($houseIds)){
                    $params['in']['house_id'] = $houseIds;
                }
            }else{
                $params['where']['house_id'] = $params['house_id'];
            }
        }
        return $params;
    }

    /**
     * @param array $list
     * @return array
     */
    protected function indexAfter(array $list): array
    {
        if (!empty($list)){
            $community = Functions::getCommunity();//社区
            $relationship = Functions::getDictDate("resident_relationship");
            $landscape = Functions::getDictDate("resident_landscape");
            //获取标签
            foreach ($list as &$item){
                //获取小区信息
                $resInfo = Functions::getQuartersInfo($item['res_id']);
                $item['res_name'] = $resInfo ? $resInfo['name'] : '未设置';
                $item['house_name'] = Functions::getHouseName($item['house_id'],1) ?? '未设置';
                $item['cname_text'] = $community[$item['cid']] ?? "--";
                $item['relationship_text'] = $relationship[$item['relationship']] ?? "--";
                $item['landscape_text'] = $landscape[$item['landscape']] ?? "--";
                $item['sex_name'] = Resident::$sex[$item['sex']] ?? "保密";
                //计算年龄
                $item['age'] = Functions::getAgeByBirthDate($item['birthday']);
//                if ($item['id_card'] != ''){
//                    $idc = JudgeIdcardNum::check($item['id_card']);
//                    if ($idc['state'] == 1){
//                        $item['age'] =$idc['age'];
//                    }
//                }
                $item['id_card_text'] = Functions::desensitizeIdCard($item['id_card']);
                $item['mobile_text'] = Functions::maskPhoneNumber($item['mobile']);
                //获取人员标签
                $tag = ResidentTag::query()->where("resident_id",$item['id'])->where("res_id",$item['res_id'])->pluck("tag_id")->toArray();
                if (!empty($tag)){
                    $tag = Label::query()->whereIn("id",$tag)->pluck("name")->toArray();
                    $item['tags_text'] = implode("，",$tag);
                }else{
                    $item['tags_text'] = "--";
                }
            }
        }
        return $list;
    }

    /**
     * 添加渲染，方便重写
     * @return View
     */
    protected function addRender(): View
    {
        //获取居民自定义字段
        $selffield = SelfField::query()->where("type",1)->get()->toArray();
        $imgIndex = $fileIndex = [];
        if (!empty($selffield)){
            foreach ($selffield as &$item){
                $item['extend'] = json_decode($item['extend'],true);
                if ($item['field_type'] == 12){
                    $imgIndex[] = $item['max_index'];
                }
                if ($item['field_type'] == 13){
                    $fileIndex[] = $item['max_index'];
                }
            }
        }
        $selffield = array_chunk($selffield,2);
        $quarters = [];
        if (C_ID > 0){
            //获取小区
            $quarters = Quarters::query()->select("id","name")->where("cid",C_ID)->get()->toArray();
        }
        return $this->render('', [
            'quarters' =>$quarters,
            'input' => $this->request->all(),
            'selffield' => $selffield,
            'imgindex' => $imgIndex,
            'fileindex' => $fileIndex
        ]);
    }

    /**
     * 编辑渲染，方便重写
     * @param array $info
     * @return View
     */
    protected function editRender(array $info ):View{
        //获取居民自定义字段
        $selffield = SelfField::query()->where("type",1)->get()->toArray();
        $imgIndex = $fileIndex = [];
        $extend = $info['extend'] && !empty(json_decode($info['extend'],true)) ? json_decode($info['extend'],true) :[];
        $extendnew = [];
        foreach ($extend as $item){
            if (in_array($item['type'],[4,6])){
                $item['value'] = isset($item['value']) && $item['value'] ? explode(",",$item['value']) : [];
            }
            $extendnew[$item['title']] = $item;
        }
        if (!empty($selffield)){
            foreach ($selffield as &$item){
                $item['extend'] = json_decode($item['extend'],true);
                if ($item['field_type'] == 12){
                    $imgIndex[] = $item['max_index'];
                }
                if ($item['field_type'] == 13){
                    $fileIndex[] = $item['max_index'];
                }
                if (isset($extendnew[$item['field_name']])){
                    if (in_array($item['field_type'],[4,6])){
                        $item['default_value'] = $extendnew[$item['field_name']]['value'] ?? [];
                    }else{
                        $item['default_value'] = $extendnew[$item['field_name']]['value'] ?? "";
                    }
                }else{
                    if (in_array($item['field_type'],[4,6])){
                        $item['default_value'] = [];
                    }else{
                        $item['default_value'] = "";
                    }
                }
            }
        }
        $selffield = array_chunk($selffield,2);
        $quarters = [];
        if (C_ID > 0){
            //获取小区
            $quarters = Quarters::query()->select("id","name")->where("cid",C_ID)->get()->toArray();
        }
        if ($info['cid'] > 0){
            //获取小区
            $quarters = Quarters::query()->select("id","name")->where("cid",$info['cid'])->get()->toArray();
        }
        //获取housename
        $house = House::bFind($info['house_id']);
        return $this->render('', [
            'input' => $this->request->all(),
            'info' => $info,//"building","unit","floor","sn"
            'house_name' => !empty($house) ? $house['building']."栋".$house['unit']."单元".$house['floor'].'楼'.$house['sn'].'号' : '楼栋-单元-楼层-房号',
            'quarters' =>$quarters,
            'tags' => ResidentTag::query()->where("resident_id",$info['id'])->pluck("tag_id")->toArray(),
            'selffield' => $selffield,
            'imgindex' => $imgIndex,
            'fileindex' => $fileIndex
        ]);
    }

    /**
     * @param array $data
     * @param string $type
     * @return array|string
     */
    protected function saveBefore(array $data, string $type): array|string
    {
        //重新设置数据
        $count = 0;
        $num = 0;
        $data['tag'] = isset($data['tag']) ? $data['tag'] : [];
        if (isset($data['extend']) && !empty($data['extend'])){
            foreach ($data['extend'] as &$item){
                $count++;
                $item['value'] = $item['value'] ?? '';
                if (is_array($item['value'])){
                    $item['value'] = implode(",",$item['value']);
                }
                if (isset($item['value']) && $item['value'] != ""){
                    $num++;
                }
            }
            $data['extend'] = json_encode(array_values($data['extend']));
        }
        foreach ($data as $key=>$value){
            if ($key != 'extend'){
                if (!is_array($value)){
                    $count++;
                    if ($value != ""){
                        $num++;
                    }
                }else{
                    $count++;
                    if (!empty($value)){
                        $num++;
                    }
                }
            }
        }
        $data['integrity'] = intval(min(ceil(($num/$count)*100),100));
        return $data;
    }

    /**
     * @param array $data
     * @param string $type
     * @return void
     */
    protected function saveAfter(array $data, string $type): void
    {
        if (isset($data['tag']) && !empty($data['tag'])){
            $insert = [];
            sort($data['tag']);
            foreach ($data['tag'] as $item){
                $insert[] = [
                    'resident_id' => $data['id'],
                    'tag_id' => $item,
                    'res_id' => $data['res_id'],
                    'cid' => $data['cid'],
                    'house_id' => $data['house_id'],
                    'create_time' => date("Y-m-d H:i:s"),
                    'update_time' => date("Y-m-d H:i:s")
                ];
            }
        }
        if ($type == 'edit'){
            //获取原标签数据
            $oldTag = ResidentTag::query()->where("resident_id",$data['id'])
                ->orderBy("tag_id","asc")->pluck("tag_id")->toArray();
            $tagStr = $tagOldStr = "";
            if (!empty($data['tag'])){
                $tagStr = implode(",", $data['tag']);
            }
            if (!empty($oldTag)){
                $tagOldStr = implode(",", $oldTag);
            }
            if (($tagStr != '' || $tagOldStr != '') && $tagStr != $tagOldStr){
                $oldtagarr = Label::query()->whereIn("id",$oldTag)->pluck("name")->toArray();
                $tagarr = Label::query()->whereIn("id",$data['tag'])->pluck("name")->toArray();
                $insertLog = [
                    "field" => "标签",
                    'before_value' => !empty($oldtagarr) ? implode(",", $oldtagarr) : "",
                    "after_value" =>!empty($tagarr) ? implode(",", $tagarr) : "",
                ];
                $insertLog['type'] = 1; //
                $insertLog['resident_id'] = $data['id'];
                $insertLog['house_id'] = $data['house_id'];
                $insertLog['user_name'] = $data['user_name'];
                $insertLog['op_user'] = LoginAuth::adminLoginInfo("info.name");
                $insertLog['op_datetime'] = date("Y-m-d H:i:s");
                $insertLog['hold_date'] = date("Y-m-d");
            }
            if (!empty($insertLog))
                ResidentLog::query()->insert($insertLog);
        }
        if($type == 'add'){
            $insertLog = [
                'resident_id' =>$data['id'],
                'user_name' =>$data['user_name'],
                'op_user' =>LoginAuth::adminLoginInfo("info.name"),
                'op_datetime' =>date("Y-m-d H:i:s"),
                'field' =>"标签",
                'before_value' =>"",
                'after_value' =>"",
                'hold_date' =>date("Y-m-d"),
                'house_id' =>$data['house_id'],
                'type' =>2,
            ];
            ResidentLog::query()->insert($insertLog);
        }
        //删除数据
        ResidentTag::query()->where("resident_id",$data['id'])->delete();
        if (!empty($insert)){
            ResidentTag::query()->insert($insert);
        }
    }

    /**
     * @return JsonResponse
     */
    public function ajaxres(){
        $c_id = $this->request->post("c_id",0);
        if (!$c_id)
            return Result::success("",[]);
        $list = Quarters::query()->select("id","name")->where("cid",$c_id)->get()->toArray();
        return Result::success("",$list);
    }

    /**
     * @return JsonResponse
     */
    public function ajaxqes(){
        $id = $this->request->get("id",0);
        $type = $this->request->get("type",2);
        return Result::success("",Functions::getHouseTree($id,$type));
    }

    /**
     * 检验身份证
     * @return JsonResponse
     */
    public function ajaxverifyidcard(){
        $id = $this->request->post("value",0);
        return Result::success("",JudgeIdcardNum::check($id));
    }

    /**
     * 详情页
     * @return View|JsonResponse
     * @author: duyang
     * @time: 2022/11/9 14:37
     */
    public function detail()
    {
        $id = $this->request->get('id',0);
        if (!$id) {
            return $this->toError('参数错误');
        }
        $info = $this->selfQuery($this->model::getInstance()->detailRange())->where($this->model::primaryKey(),$id)->first();
        if (!$info) {
            return $this->toError('信息不存在');
        }
        $info = $info->toArray();

        $info['extend'] = $info['extend'] && !empty(json_decode($info['extend'],true)) ? array_chunk(json_decode($info['extend'],true),2,true) :[];
        $info['house'] = Functions::getHouseName($info['house_id'],1,1);
        $community = Functions::getCommunity();//社区
        $relationship = Functions::getDictDate("resident_relationship");
        $landscape = Functions::getDictDate("resident_landscape");
        $info['cname_text'] = $community[$info['cid']] ?? "--";
        $info['relationship_text'] = $relationship[$info['relationship']] ?? "--";
        $info['landscape_text'] = $landscape[$info['landscape']] ?? "--";
        $info['sex_name'] = Resident::$sex[$info['sex']] ?? "保密";
        $info['age'] = '--';
        if ($info['id_card'] != ''){
            $idc = JudgeIdcardNum::check($info['id_card']);
            if ($idc['state'] == 1){
                $info['age'] = $idc['age'];
            }
        }
        return $this->render('', ['input' => $this->request->all(), 'info' => $info]);
    }

    /**
     * 导入
     * @return JsonResponse
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function import()
    {
        set_time_limit(0);
        ini_set("memory_limit", "1024M");
        $filepath = Upload::uploadXlsx();
        if (!is_string($filepath)) {
            return $filepath;
        }
        // 创建excel加载器
        $reader = IOFactory::createReader("Xlsx");
        // 加载excel文件
        $spreadsheet = $reader->load($filepath);
        // 获取指定sheet
        $worksheet = $spreadsheet->getActiveSheet();
        // 读取sheet中数据
        $data = $worksheet->toArray();
        if (empty($data)) {
            unlink($filepath);
            return Result::error('请上传数据');
        }
        unset($data[0], $data[1]);

        $successNum = 0;
        $errorNum = 0;
        try {
            $comm = array_flip(Functions::getCommunity());//社区
            $qua = Quarters::query()->pluck("id","name")->toArray();//小区
            $landscape = array_flip(DictDataService::getDataList("resident_landscape",true));//政治面貌
            $relationship = array_flip(DictDataService::getDataList("resident_relationship",true));//与户主关系
            $tag = Label::query()->pluck('id','name')->toArray();//标签

            foreach ($data as $key => $row) {
                $row = $this->trim($row);
                if ($row[0] == '' || !isset($comm[$row[0]])){
                    $errorNum++;
                    $worksheet->setCellValue('P' . ($key + 1), "社区为空或者社区不存在");
                    continue;
                }
                if ($row[1] == '' || !isset($qua[$row[1]])){
                    $errorNum++;
                    $worksheet->setCellValue('P' . ($key + 1), "小区为空或者小区不存在");
                    continue;
                }
                if ($row[2] == '' || $row[3] == '' || $row[4] == '' || $row[5] == '' || $row[6] == '' || $row[7] == ''){
                    $errorNum++;
                    $worksheet->setCellValue('P' . ($key + 1), "楼栋、单元、楼层、房号、姓名或身份证号为空");
                    continue;
                }
                //判断房号是否存在
                $houseid = House::query()->where("res_id",$qua[$row[1]])
                    ->where("building",$row[2])
                    ->where("unit",$row[3])
                    ->where("floor",$row[4])
                    ->where("sn",$row[5])->value("id");
                if (!$houseid){
                    $errorNum++;
                    $worksheet->setCellValue('P' . ($key + 1), "房号不存在！");
                    continue;
                }
                $tags = [];
                if ($row[14]){
                    $tagArr = explode("#",$row[14]);
                    foreach ($tagArr as $tagT){
                        $tags[] = $tag[$tagT] ?? '';
                    }
                }
                $tags = array_filter($tags);
                $checkId = JudgeIdcardNum::check($row[7]);
                $xiaoqu = [
                    'cid' => $comm[$row[0]],
                    'res_id' => $qua[$row[1]],
                    'house_id' => $houseid,
                    'user_name' => $row[6],
                    'birthday' => $row[9] ?? ($checkId['state'] == 1 ? $checkId['birthday'] : ''),
                    'sex' => $row[8] == '男' ? '1' : ($checkId['state'] == 1 ? $checkId['gender'] : 0),
                    'mobile' => $row[10] ?? '',
                    'mobile2' => $row[11] ?? '',
                    'id_card' => $row[7],
                    'relationship' => $relationship[$row[13]] ?? '',
                    'landscape' => $landscape[$row[12]] ?? '',
                    'integrity' => '60',
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ];
                $res_id = Resident::query()->insertGetId($xiaoqu);
                if (!empty($tags)){
                    $insert = [];
                    foreach ($tags as $t){
                        $insert[] = [
                            'resident_id' => $res_id,
                            'tag_id' => $t,
                            'res_id' => $qua[$row[1]],
                            'cid' => $comm[$row[0]],
                            'house_id' => $houseid,
                            'create_time' => date('Y-m-d H:i:s'),
                            'update_time' => date('Y-m-d H:i:s')
                        ];
                    }
                    ResidentTag::query()->insert($insert);
                }
                $successNum++;
            }
        } catch (\Exception $e) {
            Log::error('居民导入失败：'.$e->getMessage());
            return Result::error("全部导入失败");
        }
        $error_url = '';
        if ($errorNum) {
            $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
            $name = "/居民导入失败" . Str::random(10) . '.xlsx';
            $date = date('Y/m/d');
            $importPath = public_path('import') . '/' . $date;
            if (!is_dir($importPath)) {
                mkdir($importPath, 0777, true);
            }
            $importPath .= $name;
            $writer->save($importPath);
            $error_url = asset('/import/' . $date . $name);
        }
        return Result::success("导入成功{$successNum}条；导入失败{$errorNum}条", [
            'successNum' => $successNum,
            'errorNum' => $errorNum,
            'error_url' => $error_url,
        ]);
    }
    protected function deleteAfter(array $data): void
    {
        ResidentTag::query()->where("resident_id",$data['id'])->delete();
    }

    /**
     * 公共新增
     * @return View|JsonResponse
     */
    public function add(): View|JsonResponse
    {
        if ($this->request->isMethod('POST')) {
            $data = $this->request->post();
            //验证
            if ($this->validate ?? false) {
                //验证前数据处理
                $data = $this->validateBefore($data,'add');
                if(!is_array($data)){
                    return Result::error($data);
                }
                $validate = new $this->validate();
                $error = $validate->setScene('add')->setData($data)->run();
                if ($error) {
                    return Result::error($error);
                }
                $data = $validate->getData();
            }
            //数据处理
            $data = $this->saveBefore($data,'add');
            if(!is_array($data)){
                return Result::error($data);
            }
            $result = $this->model::create($data);
            if (!$result->id) {
                return Result::error('保存失败');
            }

            $pk = $this->model::primaryKey();
            if ($pk) {
                $data[$pk] = $result->id;
            }
            $this->saveAfter($data, 'add');

            return Result::success('保存成功',['id'=>$data[$pk]]);
        } else {
            return $this->addRender();
        }
    }


}